'use client'
import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface WatermarkRemovalDemoProps {
  beforeImage: string
  afterImage: string
  className?: string
}

export default function WatermarkRemovalDemo({
  beforeImage,
  afterImage,
  className = '',
}: WatermarkRemovalDemoProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [showAfter, setShowAfter] = useState(false)
  const [sliderPosition, setSliderPosition] = useState(50)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleRemoveWatermark = () => {
    setIsProcessing(true)
    setShowAfter(false)

    // 模拟处理过程
    setTimeout(() => {
      setIsProcessing(false)
      setShowAfter(true)
    }, 2500)
  }

  const handleMove = (event: MouseEvent | TouchEvent) => {
    if (!isDragging || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const x = 'touches' in event ? event.touches[0].clientX : event.clientX
    const position = ((x - containerRect.left) / containerRect.width) * 100

    setSliderPosition(Math.min(Math.max(position, 0), 100))
  }

  useEffect(() => {
    const handleMouseUp = () => setIsDragging(false)
    const handleMouseMove = (e: MouseEvent) => handleMove(e)
    const handleTouchMove = (e: TouchEvent) => handleMove(e)

    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('touchmove', handleTouchMove)
      window.addEventListener('mouseup', handleMouseUp)
      window.addEventListener('touchend', handleMouseUp)
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('touchmove', handleTouchMove)
      window.removeEventListener('mouseup', handleMouseUp)
      window.removeEventListener('touchend', handleMouseUp)
    }
  }, [isDragging])

  return (
    <div className={`relative ${className}`}>
      {/* 图片对比容器 */}
      <div
        ref={containerRef}
        className="relative select-none w-full h-[500px] rounded-2xl overflow-hidden shadow-2xl border border-white/10"
        style={{ touchAction: 'none' }}
      >
        {/* 背景图片 (After) */}
        <div className="absolute inset-0">
          <img
            src={afterImage}
            alt="After removing watermark"
            className="w-full h-full object-cover"
            draggable="false"
          />
          {/* 模拟水印被移除后的效果 */}
          {!showAfter && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white/80 px-8 py-4 rounded-lg text-gray-800 font-bold text-2xl transform rotate-12 opacity-60">
                SAMPLE WATERMARK
              </div>
            </div>
          )}
        </div>

        {/* 前景图片 (Before) - 只在非处理状态下显示 */}
        {!isProcessing && (
          <div
            className="absolute inset-0 transition-all duration-300"
            style={{
              clipPath: showAfter
                ? `inset(0 ${100 - sliderPosition}% 0 0)`
                : 'inset(0 0 0 0)',
            }}
          >
            <img
              src={beforeImage}
              alt="Before removing watermark"
              className="w-full h-full object-cover"
              draggable="false"
            />
            {/* 模拟原始水印 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white/80 px-8 py-4 rounded-lg text-gray-800 font-bold text-2xl transform rotate-12 opacity-60">
                SAMPLE WATERMARK
              </div>
            </div>
          </div>
        )}

        {/* 处理中显示原始图片 */}
        {isProcessing && (
          <div className="absolute inset-0">
            <img
              src={beforeImage}
              alt="Processing watermark removal"
              className="w-full h-full object-cover"
              draggable="false"
            />
            {/* 模拟原始水印 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white/80 px-8 py-4 rounded-lg text-gray-800 font-bold text-2xl transform rotate-12 opacity-60">
                SAMPLE WATERMARK
              </div>
            </div>
          </div>
        )}

        {/* 处理中的闪光特效 */}
        <>
          {isProcessing && (
            <>
              {/* 扫描线 */}
              <motion.div
                className="absolute top-0 bottom-0 w-1 bg-gradient-to-b from-purple-500 via-pink-500 to-pink-500"
                initial={{ left: '0%' }}
                animate={{ left: '100%' }}
                exit={{ opacity: 0 }}
                transition={{ duration: 2, ease: 'easeInOut' }}
              />

              {/* 闪光效果 */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-pink-500/20 to-transparent"
                initial={{ x: '-100%' }}
                animate={{ x: '100%' }}
                exit={{ opacity: 0 }}
                transition={{ duration: 1.5, ease: 'easeInOut', delay: 0.5 }}
              />

              {/* 处理中遮罩 */}
              <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-lg font-semibold">AI Processing...</p>
                  <p className="text-sm opacity-80">Removing watermark</p>
                </div>
              </div>
            </>
          )}
        </>

        {/* 滑动分割线 (只在显示对比时显示) */}
        {showAfter && !isProcessing && (
          <div
            className="absolute top-0 bottom-0 w-1 bg-gradient-to-b from-purple-500 via-pink-500 to-pink-500 cursor-ew-resize group"
            style={{
              left: `${sliderPosition}%`,
              transform: 'translateX(-50%)',
            }}
            onMouseDown={() => setIsDragging(true)}
            onTouchStart={() => setIsDragging(true)}
          >
            {/* 滑动手柄 */}
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center cursor-ew-resize transition-all duration-200 group-hover:scale-110 group-hover:shadow-xl">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                className="text-gray-600"
              >
                <path
                  d="M8 8L4 12M4 12L8 16M4 12H20M20 12L16 8M20 12L16 16"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        )}

        {/* 标签 */}
        {showAfter && (
          <>
            <div className="absolute top-4 left-4 px-4 py-2 bg-purple-500/90 backdrop-blur text-white text-sm font-medium rounded-full shadow-lg">
              With Watermark
            </div>
            <div className="absolute top-4 right-4 px-4 py-2 bg-pink-500/90 backdrop-blur text-white text-sm font-medium rounded-full shadow-lg">
              Watermark Removza
            </div>
          </>
        )}
      </div>

      {/* 控制按钮 */}
      <div className="flex justify-center mt-8">
        <motion.button
          onClick={handleRemoveWatermark}
          disabled={isProcessing}
          className="group relative px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all text-white font-bold text-lg rounded-full shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="relative z-10">
            {isProcessing
              ? 'Processing...'
              : showAfter
              ? 'Process Again'
              : 'Remove Watermark'}
          </span>

          {/* 按钮光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
        </motion.button>
      </div>
    </div>
  )
}
