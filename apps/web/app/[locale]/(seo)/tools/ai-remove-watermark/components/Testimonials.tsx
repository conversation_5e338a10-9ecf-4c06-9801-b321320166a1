'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

const testimonials = [
  {
    name: '<PERSON>',
    role: 'E-commerce Manager',
    avatar:
      'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=200&h=200&fit=crop&crop=face&auto=format',
    content:
      "This is the best tool to remove any object from a photo I've ever used. It saved me hours of tedious work in Photoshop. Truly a game-changer!",
    rating: 5,
    company: 'Fashion Forward',
  },
  {
    name: '<PERSON>',
    role: 'Social Media Creator',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    content:
      'I needed a quick TikTok watermark remover for my social media posts, and this worked perfectly. Super fast and the quality is amazing. Highly recommend.',
    rating: 5,
    company: '@mikecreatescontent',
  },
  {
    name: '<PERSON>',
    role: 'Professional Photographer',
    avatar:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    content:
      'As a photographer, I often need to remove distractions. This tool helped me learn how to remove people from photos cleanly. My clients are happier than ever.',
    rating: 5,
    company: 'Thompson Photography',
  },
  {
    name: 'David Park',
    role: 'Content Creator',
    avatar:
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    content:
      "I can't believe this powerful AI watermark remover is free. It handles even complex backgrounds with ease. A must-have for any content creator.",
    rating: 5,
    company: 'Creative Studios',
  },
  {
    name: 'Lisa Wang',
    role: 'Marketing Director',
    avatar:
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
    content:
      'The process to remove a watermark from a picture is so intuitive. I cleaned up over 50 family photos in one afternoon. The free storage is a huge bonus!',
    rating: 5,
    company: 'Digital Marketing Pro',
  },
  {
    name: 'James Wilson',
    role: 'Graphic Designer',
    avatar:
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
    content:
      'Finally, a tool that can actually remove an object from a photo without leaving a blurry mess. ImgGen is my new secret weapon for perfect images.',
    rating: 5,
    company: 'Wilson Design Co.',
  },
]

export default function Testimonials() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  return (
    <section className="w-full py-24 bg-gradient-to-b from-slate-900 to-gray-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-1/6 w-72 h-72 bg-cyan-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/6 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Trusted by Thousands of
            <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Happy Users
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            See what professionals and creators are saying about our AI
            watermark removal tool.
          </p>
        </motion.div>

        {/* 评价网格 */}
        <div className="grid overflow-hidden grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="group relative"
            >
              <div className="relative p-6 bg-white/5 backdrop-blur border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 h-full">
                {/* 引用标记 */}
                <div className="absolute top-4 right-4 text-cyan-400/30 text-4xl font-serif">
                  "
                </div>

                {/* 星级评分 */}
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg
                      key={i}
                      className="w-5 h-5 text-yellow-400 fill-current"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>

                {/* 评价内容 */}
                <p className="text-gray-300 mb-6 leading-relaxed italic">
                  {testimonial.content}
                </p>

                {/* 用户信息 */}
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-cyan-400/30"
                    />
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-slate-900"></div>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold group-hover:text-cyan-300 transition-colors duration-300">
                      {testimonial.name}
                    </h4>
                    <p className="text-gray-400 text-sm">{testimonial.role}</p>
                    <p className="text-cyan-400 text-xs">
                      {testimonial.company}
                    </p>
                  </div>
                </div>

                {/* 悬停光效 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-2xl"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部统计 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="group">
              <div className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                1M+
              </div>
              <div className="text-gray-300 font-medium">Images Processed</div>
            </div>
            <div className="group">
              <div className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                4.9★
              </div>
              <div className="text-gray-300 font-medium">Average Rating</div>
            </div>
            <div className="group">
              <div className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                50K+
              </div>
              <div className="text-gray-300 font-medium">Happy Users</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
