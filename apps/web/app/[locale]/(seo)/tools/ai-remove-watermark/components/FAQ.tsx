'use client'
import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Link } from '@i18n/routing'

// 内联SVG图标组件
const ChevronDownIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
)

const QuestionMarkCircleIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
)

const SparklesIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
    />
  </svg>
)

const faqs = [
  {
    question: 'How to remove people from photos without technical skills?',
    answer:
      "With ImgGen, you don't need any special skills. Just upload your photo, use our simple brush tool to select the person, and our AI will handle the rest. It's the easiest way to remove any object from a photo.",
  },
  {
    question: 'Is this online watermark remover truly free to use?',
    answer:
      'Yes, our core service is a completely free watermark remover. You can process your images without any cost. We believe everyone should have access to a high-quality tool to remove any object from a photo.',
  },
  {
    question: 'How does your AI watermark remover protect my privacy?',
    answer:
      'We prioritize your privacy. All uploaded images are processed securely and are automatically deleted from our servers after a short period. We never share your data, so you can confidently remove a watermark from any image.',
  },
  {
    question: 'Can I use this as a TikTok watermark remover on mobile?',
    answer:
      'Absolutely! Our tool is fully web-based and responsive, working perfectly on any mobile browser. You can use our AI watermark remover on your phone to edit images on the go.',
  },
  {
    question: "What's the best way how to remove an object from a photo?",
    answer:
      'The best way is to use a tool that intelligently reconstructs the background. Our AI watermark remover is trained on millions of images to ensure that when it removes an object, the result is natural and seamless.',
  },
  {
    question: 'How do I erase text from an image online free without blur?',
    answer:
      "Our algorithm excels at this. It doesn't just blur the text; it analyzes the surrounding pixels to rebuild the area. This makes our AI watermark remover perfect for cleaning up text from images cleanly.",
  },
]

export default function FAQ({ link = '' }) {
  const [openIndex, setOpenIndex] = useState<number | null>(null)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="relative w-full py-24 bg-gradient-to-b from-slate-900 via-purple-900/10 to-slate-900 overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={inView ? { scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6 shadow-lg shadow-purple-500/25"
          >
            <SparklesIcon className="w-8 h-8 text-white" />
          </motion.div>

          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Frequently Asked
            <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-shimmer bg-[length:200%_100%]">
              Questions
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Everything you need to know about our AI watermark removal tool and
            how it can transform your images.
          </p>
        </motion.div>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -50 }}
              animate={inView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div
                className={`faq-glass relative overflow-hidden transition-all duration-500 ${
                  openIndex === index ? 'faq-open' : ''
                }`}
              >
                {/* 渐变边框效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-500/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative bg-slate-900/80 backdrop-blur-xl rounded-2xl border border-white/10 group-hover:border-purple-400/30 transition-all duration-300">
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-8 py-6 text-left flex items-start gap-4 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-slate-900 rounded-2xl transition-all duration-300"
                  >
                    {/* 问题图标 */}
                    <div className="faq-qicon flex-shrink-0 mt-1">
                      <QuestionMarkCircleIcon className="w-5 h-5" />
                    </div>

                    {/* 问题文本 */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg md:text-xl font-semibold text-white group-hover:text-purple-300 transition-colors duration-300 leading-relaxed">
                        {faq.question}
                      </h3>
                    </div>

                    {/* 展开图标 */}
                    <motion.div
                      animate={{ rotate: openIndex === index ? 180 : 0 }}
                      transition={{ duration: 0.4, ease: 'easeInOut' }}
                      className="flex-shrink-0 mt-1"
                    >
                      <ChevronDownIcon className="w-6 h-6 text-purple-400 group-hover:text-pink-400 transition-colors duration-300" />
                    </motion.div>
                  </button>

                  <AnimatePresence>
                    {openIndex === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.4, ease: 'easeInOut' }}
                        className="overflow-hidden"
                      >
                        <div className="px-8 pb-6">
                          {/* 分割线 */}
                          <div className="faq-divider"></div>

                          {/* 答案内容 */}
                          <motion.div
                            initial={{ y: -10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                            className="relative"
                          >
                            <div className="absolute -left-2 top-0 w-1 h-full bg-gradient-to-b from-purple-500 to-pink-500 rounded-full opacity-60"></div>
                            <p className="text-gray-300 leading-relaxed text-base md:text-lg pl-6">
                              {faq.answer}
                            </p>
                          </motion.div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部CTA区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20 text-center"
        >
          <div className="relative">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>

            <div className="relative bg-slate-900/90 backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-12 shadow-2xl shadow-purple-500/10">
              {/* 装饰性图标 */}
              <motion.div
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: 'linear',
                }}
                className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mb-6 shadow-lg shadow-purple-500/25"
              >
                <SparklesIcon className="w-10 h-10 text-white" />
              </motion.div>

              <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                Still have questions?
              </h3>
              <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
                Our support team is here to help you get the most out of our AI
                watermark removal tool. Get started today and see the magic
                happen!
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Link
                    href={link}
                    prefetch={false}
                    className="relative block px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    Try AI Watermark Remover
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href="/templates"
                    prefetch={false}
                    className="block px-8 py-4 border-2 border-purple-400/50 text-white font-semibold rounded-full hover:bg-purple-500/10 hover:border-purple-400 transition-all duration-300"
                  >
                    View Documentation
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
