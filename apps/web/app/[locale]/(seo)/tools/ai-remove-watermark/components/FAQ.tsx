'use client'
import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Link } from '@i18n/routing'

const faqs = [
  {
    question: 'How to remove people from photos without technical skills?',
    answer:
      "With ImgGen, you don't need any special skills. Just upload your photo, use our simple brush tool to select the person, and our AI will handle the rest. It's the easiest way to remove any object from a photo.",
  },
  {
    question: 'Is this online watermark remover truly free to use?',
    answer:
      'Yes, our core service is a completely free watermark remover. You can process your images without any cost. We believe everyone should have access to a high-quality tool to remove any object from a photo.',
  },
  {
    question: 'How does your AI watermark remover protect my privacy?',
    answer:
      'We prioritize your privacy. All uploaded images are processed securely and are automatically deleted from our servers after a short period. We never share your data, so you can confidently remove a watermark from any image.',
  },
  {
    question: 'Can I use this as a TikTok watermark remover on mobile?',
    answer:
      'Absolutely! Our tool is fully web-based and responsive, working perfectly on any mobile browser. You can use our AI watermark remover on your phone to edit images on the go.',
  },
  {
    question: "What's the best way how to remove an object from a photo?",
    answer:
      'The best way is to use a tool that intelligently reconstructs the background. Our AI watermark remover is trained on millions of images to ensure that when it removes an object, the result is natural and seamless.',
  },
  {
    question: 'How do I erase text from an image online free without blur?',
    answer:
      "Our algorithm excels at this. It doesn't just blur the text; it analyzes the surrounding pixels to rebuild the area. This makes our AI watermark remover perfect for cleaning up text from images cleanly.",
  },
]

export default function FAQ({ link = '' }) {
  const [openIndex, setOpenIndex] = useState<number | null>(null)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="w-full py-24 bg-gradient-to-b from-gray-900 to-slate-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Your Questions,
            <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Answered
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get answers to common questions about our AI watermark removal tool
            and how it can help you.
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div className="bg-white/5 backdrop-blur border border-white/10 overflow-hidden rounded-2xl  hover:bg-white/10 transition-all duration-300">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between rounded-2xl focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-inset"
                >
                  <h3 className="text-lg font-semibold text-white group-hover:text-cyan-300 transition-colors duration-300 pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <svg
                      className="w-6 h-6 text-cyan-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </motion.div>
                </button>

                <AnimatePresence>
                  {
                    <motion.div
                      className={`overflow-hidden transition-all duration-150 ease-linear ${
                        openIndex === index ? '!h-fit' : '!h-0 !opacity-0'
                      }`}
                    >
                      <div className="px-6 pb-6">
                        <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-4"></div>
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  }
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部联系信息 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-cyan-500/10 to-purple-500/10 border border-cyan-500/20 rounded-2xl p-8">
            <h3 className="text-xl font-bold text-white mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-300 mb-6">
              Our support team is here to help you get the most out of our AI
              watermark removal tool.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Link
                  href={link}
                  prefetch={false}
                  className="block w-full h-full text-center"
                >
                  Try AI Watermark Remover
                </Link>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 border-2 border-white/30 text-white font-semibold rounded-full hover:bg-white/10 transition-all duration-300"
              >
                <Link
                  href="/templates"
                  prefetch={false}
                  className="block w-full h-full text-center"
                >
                  View Documentation
                </Link>
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
