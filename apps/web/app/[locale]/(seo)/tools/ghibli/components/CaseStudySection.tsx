import React from 'react'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

interface CaseStudySectionProps {
  toolUrl: string
}

const CaseStudySection = async ({ toolUrl }: CaseStudySectionProps) => {
  const t = await getTranslations('ghibli')
  const useCases = [
    {
      title: t('turnPhotoIntoStudioGhibliProfile'),
      description: t('over500kUsersUpdated'),
      buttonText: t('createYourAvatarNow'),
      image: '/ghibli/ghibli-3.jpg',
      alt: t('ghibliAiProfilePictureAlt'),
    },
    {
      title: t('creatingBreathtakingLandscapes'),
      description: t('dontJustUsePortraits'),
      buttonText: t('convertYourLandscapePhotos'),
      image: '/ghibli/ghibli-4.jpg',
      alt: t('ghibliAiLandscapeAlt'),
    },
    {
      title: t('howToCreateGhibliPetArt'),
      description: t('immortalizeFurryFriends'),
      buttonText: t('createYourPetPortrait'),
      image: '/ghibli/ghibli-5.jpg',
      alt: t('ghibliAiPetPortraitAlt'),
    },
    {
      title: t('designUniqueGifts'),
      description: t('createMemorableStudioGhibli'),
      buttonText: t('designSpecialGift'),
      image: '/ghibli/ghibli-6.jpg',
      alt: t('ghibliAiGiftAlt'),
    },
  ]

  return (
    <div className="relative py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {t('creativeApplicationsTitle')}
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto">
            {t('creativeApplicationsDescription')}
          </p>
        </div>

        <div className="space-y-16">
          {useCases.map((useCase, index) => (
            <div
              key={index}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-700">
                  <img
                    src={useCase.image}
                    alt={useCase.alt}
                    className="w-full h-auto"
                  />
                </div>
              </div>

              {/* Content */}
              <div
                className={`space-y-6 ${
                  index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''
                }`}
              >
                <h3 className="text-2xl md:text-3xl font-bold text-fuchsia-400">
                  {useCase.title}
                </h3>
                <p className="text-gray-300 text-lg leading-relaxed">
                  {useCase.description}
                </p>
                <div className="pt-4">
                  <Link
                    href={toolUrl}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all text-white font-semibold px-6 py-3 rounded-lg inline-block"
                  >
                    {useCase.buttonText}
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CaseStudySection
