import React from 'react'
import { UploadCloud } from 'lucide-react'
import HowToGuideSection from './components/HowToGuideSection'
import ImageCompare from '../components/ImageCompare'
import FeatureSection from './components/FeatureSection'
import CaseStudySection from './components/CaseStudySection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import type { Metadata } from 'next'
import SampleImage from './components/SampleImage'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations('sketch')
  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
    keywords: t('pageKeywords'),
    openGraph: {
      title: t('pageTitle'),
      description: t('pageDescription'),
      url: 'https://imggen.org/tools/sketch',
      type: 'website',
      images: [
        {
          url: 'https://imggen.org/images/sketch-to-image-preview.png',
          width: 1200,
          height: 630,
          alt: t('pageTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('pageTitle'),
      description: t('pageDescription'),
      images: ['https://imggen.org/images/sketch-to-image-preview.png'],
    },
  }
}

const SketchToImagePage = async () => {
  const t = await getTranslations('sketch')

  // JSON-LD Schema
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('pageTitle'),
    description: t('pageDescription'),
    applicationCategory: 'PhotoApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    image: 'https://imggen.org/images/sketch-to-image-preview.png',
    keywords: t('pageKeywords').split(', '),
  }

  // 统一配置跳转URL
  const SKETCH_TOOL_URL = '/ai/sketch-to-image'

  return (
    <div className="relative h-full w-full bg-[#0f172a]">
      <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>
      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '8s', animationDelay: '2s' }}
        ></div>
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '7s', animationDelay: '4s' }}
        ></div>
      </div>

      {/* Add JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className="relative z-10 my-20">
        <div className="relative container mx-auto px-4 md:px-8 py-12 md:py-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column */}
            <div className="text-white animate-fade-in-up">
              <h1 className="text-3xl md:text-5xl font-bold mb-6 text-fuchsia-400">
                {t('heroTitle')}
              </h1>
              <p className="text-lg md:text-xl text-gray-400 mb-12 font-normal">
                {t('heroDescription')}
              </p>

              {/* Upload Section */}
              <div className="w-full group [perspective:1000px]">
                <div className="border-2 border-dashed border-gray-700 rounded-xl p-8 bg-slate-900/20 backdrop-blur-sm transition-all duration-500 group-hover:border-purple-500 [transform:rotateX(0deg)] group-hover:[transform:rotateX(10deg)]">
                  <div className="flex flex-col items-center">
                    <div className="relative">
                      <Link href={SKETCH_TOOL_URL}>
                        <button className="relative inline-block p-px font-semibold leading-6 text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all shadow-2xl cursor-pointer group rounded-xl shadow-zinc-900 transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95">
                          <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>

                          <span className="relative z-10 block px-6 py-3 rounded-xl bg-slate-950 text-gray-400 hover:text-gray-300 hover:font-semibold font-normal">
                            <div className="relative z-10 flex items-center space-x-2">
                              <UploadCloud className="w-5 h-5 transition-transform duration-500" />
                              <span className="transition-all duration-500">
                                {t('heroUpload')}
                              </span>
                            </div>
                          </span>
                        </button>
                      </Link>
                    </div>
                    <p className="text-gray-400 mt-4">{t('heroDrag')}</p>
                  </div>
                </div>
              </div>

              <SampleImage />

              {/* 移动端专用图片比较器（仅在lg屏幕以下显示） */}
              <div className="block lg:hidden mt-12 animate-fade-in-up">
                <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-800">
                  <ImageCompare
                    leftImage="/samples/sketch-before.webp"
                    rightImage="/samples/sketch-after.webp"
                    sliderPositionPercentage={0.5}
                  />
                </div>
              </div>
            </div>

            {/* Right Column - Image Compare - 保持原样，仅在大屏幕显示 */}
            <div
              className="hidden lg:block animate-fade-in-up"
              style={{ animationDelay: '0.5s' }}
            >
              <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-800">
                <ImageCompare
                  leftImage="/samples/sketch-before.webp"
                  rightImage="/samples/sketch-after.webp"
                  sliderPositionPercentage={0.5}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <FeatureSection toolUrl={SKETCH_TOOL_URL} />
      <CaseStudySection toolUrl={SKETCH_TOOL_URL} />
      <HowToGuideSection toolUrl={SKETCH_TOOL_URL} />
      <TestimonialSection toolUrl={SKETCH_TOOL_URL} />
      <FAQSection toolUrl={SKETCH_TOOL_URL} />
    </div>
  )
}

export default SketchToImagePage
