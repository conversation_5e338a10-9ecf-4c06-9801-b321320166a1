'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@ui/components/button'
import {
  Edit3,
  Zap,
  Download,
  ArrowRight,
  Sparkles,
  CheckCircle,
} from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection = ({ toolUrl }: HowToGuideSectionProps) => {
  const t = useTranslations('aiTextToImage')
  const [isVisible, setIsVisible] = useState(false)
  const [activeStep, setActiveStep] = useState(0)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    if (isVisible) {
      const interval = setInterval(() => {
        setActiveStep((prev) => (prev + 1) % 2)
      }, 3000)
      return () => clearInterval(interval)
    }
  }, [isVisible])

  const steps = [
    {
      number: t('howToStep1Number'),
      icon: Edit3,
      title: t('howToStep1Title'),
      description: t('howToStep1Description'),
      image: '/images/ai-text-to-image/tree.png',
      isVideo: false,
      tips: [
        t('howToStep1Tip1'),
        t('howToStep1Tip2'),
        t('howToStep1Tip3'),
        t('howToStep1Tip4'),
      ],
    },
    {
      number: t('howToStep2Number'),
      icon: Zap,
      title: t('howToStep2Title'),
      description: t('howToStep2Description'),
      image: '/images/ai-text-to-image/upload1.png',
      isVideo: false,
      tips: [
        t('howToStep2Tip1'),
        t('howToStep2Tip2'),
        t('howToStep2Tip3'),
        t('howToStep2Tip4'),
      ],
    },
  ]

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-slate-900 to-slate-800 relative overflow-hidden"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute bottom-20 left-10 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        />

        {/* 连接线装饰 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-px h-32 bg-gradient-to-b from-transparent via-purple-500/30 to-transparent" />
        </div>
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 标题部分 */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-flex items-center gap-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-4 py-2 text-purple-300 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            {t('howToTagText')}
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('howToTitle')}{' '}
            <span className="text-pink-400 ">{t('howToTitleHighlight')}</span>{' '}
            {t('howToTitleEnd')}
          </h2>

          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {t('howToDescription')}
          </p>
        </div>

        {/* 步骤展示 */}
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = activeStep === index

              return (
                <div
                  key={index}
                  className={`transition-all duration-1000 ${
                    isVisible
                      ? 'opacity-100 translate-y-0'
                      : 'opacity-0 translate-y-10'
                  }`}
                  style={{ transitionDelay: `${index * 200}ms` }}
                >
                  <div
                    className={`relative group ${
                      isActive ? 'scale-105' : ''
                    } transition-transform duration-500`}
                  >
                    {/* 步骤卡片 */}
                    <div
                      className={`relative bg-slate-800/50 backdrop-blur-sm border rounded-2xl p-8 transition-all duration-500 ${
                        isActive
                          ? 'border-purple-500/50 shadow-lg shadow-purple-500/20'
                          : 'border-slate-700/50 hover:border-purple-500/30'
                      }`}
                    >
                      {/* 步骤编号 */}
                      <div className="absolute -top-4 -left-4">
                        <div
                          className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg transition-all duration-300 ${
                            isActive
                              ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white scale-110'
                              : 'bg-slate-700 text-gray-300'
                          }`}
                        >
                          {step.number}
                        </div>
                      </div>

                      {/* 图片部分 */}
                      <div className="relative mb-6 overflow-hidden rounded-xl">
                        {step.isVideo ? (
                          <video
                            playsInline
                            muted
                            autoPlay
                            loop
                            src={step.image}
                            className={`w-full h-48 object-cover transition-all duration-500 ${
                              isActive ? 'scale-110' : 'scale-100'
                            }`}
                          ></video>
                        ) : (
                          <img
                            src={step.image}
                            alt={step.title}
                            className={`w-full h-48 object-cover transition-all duration-500 ${
                              isActive ? 'scale-110' : 'scale-100'
                            }`}
                          />
                        )}

                        {/* 图标覆盖 */}
                        <div
                          className={`absolute top-4 right-4 rounded-full p-3 shadow-lg transition-all duration-300 ${
                            isActive
                              ? 'bg-gradient-to-r from-purple-600 to-pink-600 scale-110'
                              : 'bg-slate-800/80'
                          }`}
                        >
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                      </div>

                      {/* 内容部分 */}
                      <div className="space-y-4">
                        <h3
                          className={`text-2xl font-bold leading-tight transition-colors duration-300 ${
                            isActive ? 'text-purple-300' : 'text-white'
                          }`}
                        >
                          {step.title}
                        </h3>

                        <p className="text-gray-300 leading-relaxed">
                          {step.description}
                        </p>

                        {/* 提示列表 */}
                        <div className="grid grid-cols-2 gap-2 pt-4">
                          {step.tips.map((tip, tipIndex) => (
                            <div
                              key={tipIndex}
                              className={`flex items-center gap-2 text-sm transition-all duration-300 ${
                                isActive ? 'text-purple-300' : 'text-gray-400'
                              }`}
                            >
                              <CheckCircle
                                className={`w-4 h-4 flex-shrink-0 transition-colors duration-300 ${
                                  isActive ? 'text-green-400' : 'text-gray-500'
                                }`}
                              />
                              <span>{tip}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 活跃指示器 */}
                      {isActive && (
                        <div className="absolute -top-2 -right-2 w-4 h-4 bg-purple-400 rounded-full animate-ping" />
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* 步骤指示器 */}
          <div className="flex justify-center mt-12 gap-3">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveStep(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  activeStep === index
                    ? 'bg-purple-400 w-8'
                    : 'bg-gray-600 hover:bg-gray-500'
                }`}
              />
            ))}
          </div>
        </div>

        {/* 底部CTA */}
        <div
          className={`text-center mt-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('howToBottomTitle')}
            </h3>
            <p className="text-gray-300 mb-6">{t('howToBottomDescription')}</p>
            <Link href={toolUrl}>
              <Button
                size="lg"
                className="group max-md:whitespace-nowrap max-md:text-xs mx-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105"
              >
                <Zap className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                {t('howToBottomButton')}
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
