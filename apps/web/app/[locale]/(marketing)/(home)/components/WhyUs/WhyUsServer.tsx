import { getTranslations } from 'next-intl/server'
import WhyUsClient from './WhyUsClient'

export default async function WhyUs() {
  const t = await getTranslations()
  const tabs = [
    {
      key: 'easy',
      label: t('home-whyus.easyTools'),
      image:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/8df3bf94-8926-4814-b10c-aac27c1524cd.jpeg',
      alt: 'easy tools - free AI image editor',
      title: t('home-whyus.smartToolsSimple'),
      desc: t('home-whyus.editLikePro'),
      button: t('home-whyus.getStarted'),
      href: '/templates',
    },
    {
      key: 'ai',
      label: t('home-whyus.smartAI'),
      image:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/489c4ca8-ac7b-4b75-9b21-f8c9eeb84e46.jpeg',
      alt: 'smart AI - free AI image editor',
      title: t('home-whyus.strongAIDelivers'),
      desc: t('home-whyus.imggenBuilt'),
      button: t('home-whyus.getStarted'),
      href: '/templates',
    },
    {
      key: 'nolimits',
      label: t('home-whyus.noLimits'),
      image:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/04226148-8913-4165-8e32-2906d9517924.jpeg',
      alt: 'no limits - free AI image editor',
      title: t('home-whyus.everythingFree'),
      desc: t('home-whyus.enjoyFullAccess'),
      button: t('home-whyus.getStarted'),
      href: '/templates',
    },
    {
      key: 'allinone',
      label: t('home-whyus.allInOne'),
      image:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/c682d62e-c92c-44c9-986c-06580c451cc9.jpeg',
      alt: 'all in one place - free AI image editor',
      title: t('home-whyus.allInOneHub'),
      desc: t('home-whyus.fromAIEditor'),
      button: t('home-whyus.getStarted'),
      href: '/templates',
    },
  ]
  return (
    <WhyUsClient
      title={t('home-whyus.whyMillionsUse')}
      subtitle={t('home-whyus.everythingYouLove')}
      tabs={tabs}
    />
  )
}
