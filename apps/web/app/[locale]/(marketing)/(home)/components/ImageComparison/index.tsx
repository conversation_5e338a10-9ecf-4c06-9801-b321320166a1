'use client'

import { useState, useCallback, useEffect, useMemo } from 'react'
import ReactCompareImage from 'react-compare-image'
import { motion } from 'framer-motion'
import Masonry from 'react-masonry-css'
import { useAtom } from 'jotai'
import {
  selectedStyleAtom,
  formDataAtom,
  generationModeAtom,
} from '@marketing/home/<USER>'
import type { StyleType } from '@marketing/home/<USER>'
import { useTranslations } from 'next-intl'

// 添加银色闪烁动画样式和隐藏滚动条样式
const silverShimmerStyle = `
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  @keyframes silverShimmer {
    0% { 
      background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    25% { 
      background: linear-gradient(135deg, #d0d0d0, #f0f0f0, #b0b0b0, #e0e0e0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    50% { 
      background: linear-gradient(135deg, #e0e0e0, #f8f8f8, #c0c0c0, #f0f0f0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    75% { 
      background: linear-gradient(135deg, #d0d0d0, #f0f0f0, #b0b0b0, #e0e0e0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    100% { 
      background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
  }

  /* 隐藏滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
`

// 将比例字符串转换为数值
const getRatioValue = (ratio: string) => {
  const [width, height] = ratio.split(':').map(Number)
  return width / height
}

interface ImageComparisonProps {
  hideCreateButton?: boolean
  hideFilterBar?: boolean
}

const ImageComparison = ({
  hideCreateButton = false,
  hideFilterBar = false,
}: ImageComparisonProps) => {
  const t = useTranslations()
  const [activeFilter, setActiveFilter] = useState('Ghibli Style')
  const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>(
    {}
  )
  const [, setSelectedStyle] = useAtom(selectedStyleAtom)
  const [formData, setFormData] = useAtom(formDataAtom)
  const [generationMode, setGenerationMode] = useAtom(generationModeAtom)
  const customStyles: StyleType[] = [
    // Ghibli Style
    {
      id: 1,
      title: t('styles.ghibliWoman'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.38.57.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.39.06.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 2,
      title: t('styles.ghibliChild'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.39.24.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.39.32.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 3,
      title: t('styles.ghibliCouple'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.39.44.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.39.52.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 4,
      title: t('styles.ghibliDog'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.40.08.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/iShot_2025-07-05_01.40.16.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Old Photo Restoration
    {
      id: 5,
      title: t('styles.restoreLittleGirl'),
      category: 'Old Photo Restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 6,
      title: t('styles.restoreOldLady'),
      category: 'Old Photo Restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 7,
      title: t('styles.restoreSoldier'),
      category: 'Old Photo Restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 8,
      title: t('styles.restoreFamily'),
      category: 'Old Photo Restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Background Removal
    {
      id: 9,
      title: t('styles.removeLipstickBG'),
      category: 'Background Removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 10,
      title: t('styles.removeBagBG'),
      category: 'Background Removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 11,
      title: t('styles.removeWomanBG'),
      category: 'Background Removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 12,
      title: t('styles.removeDogBG'),
      category: 'Background Removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Color Correction
    {
      id: 13,
      title: t('styles.fixCatColors'),
      category: 'Color Correction',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 14,
      title: t('styles.fixTemperature'),
      category: 'Color Correction',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 15,
      title: t('styles.fixRestaurant'),
      category: 'Color Correction',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 16,
      title: t('styles.fixSofaColors'),
      category: 'Color Correction',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Age Progression
    {
      id: 17,
      title: t('styles.ageBoyToAdult'),
      category: 'Age Progression',
      type: 'image-to-image',
      prompt: 'Show how this person would look in the future',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 18,
      title: t('styles.ageWomanToElder'),
      category: 'Age Progression',
      type: 'image-to-image',
      prompt: 'Show how this person would look in the future',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 19,
      title: t('styles.ageCouple'),
      category: 'Age Progression',
      type: 'image-to-image',
      prompt: 'Show how this person would look in the future',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 20,
      title: t('styles.ageFriends'),
      category: 'Age Progression',
      type: 'image-to-image',
      prompt: 'Show how this person would look in the future',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/E-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
  ]
  // 使用自定义风格数据
  const styles = useMemo(() => customStyles, [])

  const categoriesMap = {
    'Ghibli Style': t('ImageComparison.categories.Portrait'),
    'Old Photo Restoration': t('ImageComparison.categories.Vintage'),
    'Background Removal': t('ImageComparison.categories.Product'),
    'Color Correction': t('ImageComparison.categories.Art'),
    'Age Progression': t('ImageComparison.categories.Vintage'),
  }

  // 预加载图片并跟踪加载状态
  const preloadImages = useCallback((style: StyleType) => {
    let loadedCount = 0
    const checkBothLoaded = () => {
      loadedCount++
      if (loadedCount === 2) {
        setLoadingStates((prev) => ({
          ...prev,
          [style.id]: false,
        }))
      }
    }

    // 预加载原始图片
    const originalImg = new Image()
    originalImg.onload = checkBothLoaded
    originalImg.onerror = checkBothLoaded // 处理加载失败的情况
    originalImg.src = style.originalImage

    // 预加载生成的图片
    const generatedImg = new Image()
    generatedImg.onload = checkBothLoaded
    generatedImg.onerror = checkBothLoaded // 处理加载失败的情况
    generatedImg.src = style.generatedImage
  }, [])

  // 初始化加载状态并开始预加载
  useEffect(() => {
    if (styles.length > 0) {
      const initialLoadingStates = styles.reduce((acc, style) => {
        acc[style.id] = true
        return acc
      }, {} as Record<number, boolean>)
      setLoadingStates(initialLoadingStates)

      // 开始预加载所有图片
      styles.forEach((style) => {
        preloadImages(style)
      })
    }
  }, [styles, preloadImages])

  // 处理风格选择
  const handleStyleSelect = (style: StyleType) => {
    // 切换到 image-to-image 模式，因为 ImageComparison 中的风格都是 image-to-image 类型
    if (generationMode !== 'image-to-image') {
      setGenerationMode('image-to-image')
      // 保存模式到 localStorage
      localStorage.setItem(
        'image_converter_mode',
        JSON.stringify('image-to-image')
      )
    }

    setSelectedStyle(style)
    setFormData((prev) => {
      if (!prev) {
        // 如果之前没有表单数据，创建新的表单数据
        return {
          prompt: style.prompt,
          ratio: '3:2', // 设置默认比例
          nVariants: '1',
        }
      }
      // 如果已有表单数据，只更新 prompt
      return {
        ...prev,
        prompt: style.prompt,
      }
    })

    // 平滑滚动到图片生成器位置
    const imageConverter = document.querySelector('.image-style-converter')
    if (imageConverter) {
      imageConverter.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  // 根据当前筛选器筛选风格
  const filteredStyles = styles.filter(
    (style) => style.category === activeFilter
  )

  // 获取所有有图片的类别
  const availableCategories: string[] = []
  styles.forEach((style) => {
    if (!availableCategories.includes(style.category)) {
      availableCategories.push(style.category)
    }
  })

  // 瀑布流的断点设置
  const breakpointColumnsObj = {
    default: 4, // 默认4列
    1100: 3, // 宽度 >= 1100px 时为3列
    768: 2, // 宽度 >= 768px 时为2列
    500: 1, // 宽度 < 500px 时为1列
  }

  return (
    <div className="w-full px-4 md:px-0">
      <style dangerouslySetInnerHTML={{ __html: silverShimmerStyle }} />
      <div className="max-w-7xl mx-auto">
        {/* 风格选择器 */}
        {!hideFilterBar && (
          <div className="mb-8 overflow-x-auto pb-2 scrollbar-hide">
            <div className="flex justify-center gap-4 min-w-max">
              {availableCategories.map((category) => (
                <button
                  key={category}
                  className={`px-5 py-2 rounded-full font-semibold transition-all duration-300
                    ${
                      activeFilter === category
                        ? 'text-gray-800 backdrop-blur-md'
                        : 'bg-[#1a1a2e] text-gray-400 hover:bg-[#2a2a3e] hover:text-gray-300'
                    }
                  `}
                  style={
                    activeFilter === category
                      ? {
                          animation: 'silverShimmer 2s ease-in-out infinite',
                          background:
                            'linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0)',
                          boxShadow:
                            '0 4px 15px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.1)',
                        }
                      : {}
                  }
                  onClick={() => setActiveFilter(category)}
                >
                  {categoriesMap[category as keyof typeof categoriesMap]}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* 使用react-masonry-css组件实现瀑布流 */}
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className="flex w-auto -ml-6" // 负外边距抵消每个卡片的左内边距
          columnClassName="pl-6" // 每列添加左内边距实现间距
        >
          {filteredStyles.map((style) => (
            <motion.div
              key={style.id}
              className="rounded-2xl overflow-hidden mb-6 border border-white/10 shadow-lg"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: style.id * 0.05 }}
            >
              <div className="bg-[#1a1a2e] rounded-2xl">
                <div className="relative z-10">
                  {/* 卡片标题和分类标签 */}
                  <div className="p-4 pb-2">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-white">
                        {style.title}
                      </h3>
                      <span className="px-3 py-1 bg-transparent border border-purple-400 text-purple-300 text-xs rounded-full font-medium">
                        {(() => {
                          // 特殊处理某些图片的标签
                          if (
                            style.title === 'Remove Woman BG' ||
                            style.title === 'Remove Dog BG'
                          ) {
                            return 'Remove'
                          }
                          return categoriesMap[
                            style.category as keyof typeof categoriesMap
                          ]
                        })()}
                      </span>
                    </div>
                  </div>

                  {/* 图片比较容器 - 固定高度与HeroCarousel保持一致 */}
                  <div className="w-full relative">
                    {loadingStates[style.id] ? (
                      <div className="w-full h-[360px] sm:h-[400px] md:h-[440px] bg-[#1a1a2e] animate-pulse flex items-center justify-center">
                        <div className="w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    ) : (
                      <motion.div
                        className="w-full h-[360px] sm:h-[400px] md:h-[440px]"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5 }}
                      >
                        <div className="h-full">
                          <ReactCompareImage
                            leftImage={style.originalImage}
                            rightImage={style.generatedImage}
                            sliderLineWidth={2}
                            sliderLineColor="#8b5cf6"
                            sliderPositionPercentage={0.3}
                            handle={
                              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-fuchsia-600 via-purple-500 to-indigo-500 flex items-center justify-center shadow-md rotate-90">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                  className="w-5 h-5 text-white"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                                  />
                                </svg>
                              </div>
                            }
                            hover={false}
                          />
                        </div>
                      </motion.div>
                    )}
                  </div>

                  {/* 底部信息区 - 仅当按钮可见时才渲染 */}
                  {style.showButton && !hideCreateButton && (
                    <div className="p-4">
                      <div className="flex justify-center">
                        <div className="relative group">
                          <button
                            onClick={() => handleStyleSelect(style)}
                            className="relative inline-block p-px font-semibold leading-6 text-white bg-neutral-900 shadow-2xl cursor-pointer rounded-2xl shadow-purple-950 transition-all duration-300 ease-in-out hover:scale-105 active:scale-95 hover:shadow-purple-800"
                          >
                            <span className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                            <span className="relative z-10 block px-6 py-3 rounded-2xl bg-neutral-950">
                              <div className="relative z-10 flex items-center space-x-3">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 10V3L4 14h7v7l9-11h-7z"
                                  />
                                </svg>
                                <span className="transition-all duration-500 group-hover:translate-x-1.5 group-hover:text-purple-300">
                                  {t('pricingBottomArea.createButton')}
                                </span>
                              </div>
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </Masonry>

        {/* 加载更多按钮 */}
        {/* <div className="mt-10 text-center">
        <button className="px-6 py-3 bg-white border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 transition-all shadow-sm">
          加载更多风格
        </button>
      </div> */}
      </div>
    </div>
  )
}

export default ImageComparison
