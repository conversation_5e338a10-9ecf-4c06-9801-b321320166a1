import { getTranslations } from 'next-intl/server'
import Image from 'next/image'
import TestimonialSection from './components/TestimonialSection'
import FaqSectionPro from './components/FaqSectionPro'
import CtaSection from '@/[locale]/components/CtaSection'
import ScrollToTopButton from './components/ScrollToTopButton'
import FeatureShowcase from './components/FeatureShowcase'
import MoreFeatures from './components/MoreFeatures'
import WhyUs from './components/WhyUs'
import UserCase from './components/UserCase'
import ForEveryoneSection from './components/ForEveryone/ForEveryoneSection'
import InterestRecommendationSection from './components/InterestRecommendationSection'
import ScrollAnimation from './components/ScrollAnimation/ScrollAnimation'
import PopularFeatures from './components/PopularFeatures'
import HeroButtons from './components/HeroButtons'
import HeroCarousel from './components/HeroCarousel'
import TrustLogo from './components/TrustLogo'
import './home-gradient-border.css'
import './scroll-animations.css'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('home-image-t'),
    description: t('home-image-d'),
    keywords: t('home-image-k'),
  }
}

const Index = async () => {
  const t = await getTranslations()
  return (
    <div className="w-full bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033] min-h-screen text-white">
      <div className="relative pt-24 sm:pt-24 min-h-screen bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033] flex flex-col">
        <div className="relative w-full max-w-[1300px] mx-auto px-4 md:px-8 flex-1 flex flex-col justify-center">
          {/* Hero主要内容区 */}
          <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12 mb-12">
            {/* Hero区：左右分栏 */}
            <div className="flex-1 flex flex-col items-start justify-center text-left">
              <ScrollAnimation direction="down" delay={100} duration={1000}>
                <h1 className="relative text-3xl sm:text-4xl md:text-5xl lg:text-6xl py-2 font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] leading-tight">
                  {t('home.title')}
                </h1>
                <p className="text-base sm:text-lg md:text-xl font-normal max-w-2xl mt-4 sm:mt-6 md:mt-7 relative text-gray-300 leading-relaxed">
                  {t('home.description')}
                </p>
              </ScrollAnimation>
              <ScrollAnimation direction="up" delay={600} duration={800}>
                <HeroButtons />
              </ScrollAnimation>
            </div>

            {/* 右侧轮播图区 - 添加装饰框架 */}
            <div className="flex-1 flex items-center justify-center md:justify-end relative w-full min-h-[360px] sm:min-h-[400px] md:min-h-[440px]">
              <div className="relative w-full max-w-[380px] md:max-w-[480px]">
                {/* 装饰角落 */}
                <div className="absolute -top-3 -left-3 w-8 h-8 border-t-2 border-l-2 border-indigo-500/70"></div>
                <div className="absolute -top-3 -right-3 w-8 h-8 border-t-2 border-r-2 border-indigo-500/70"></div>
                <div className="absolute -bottom-3 -left-3 w-8 h-8 border-b-2 border-l-2 border-indigo-500/70"></div>
                <div className="absolute -bottom-3 -right-3 w-8 h-8 border-b-2 border-r-2 border-indigo-500/70"></div>

                {/* 内容容器 */}
                <div
                  className="relative rounded-lg overflow-hidden
                    bg-gray-800/80 backdrop-blur-sm
                    p-3 md:p-5
                    shadow-[0_8px_30px_rgba(0,0,0,0.25)]"
                >
                  <ScrollAnimation
                    className="w-full"
                    direction="fade"
                    delay={400}
                    duration={1200}
                  >
                    <HeroCarousel />
                  </ScrollAnimation>
                </div>
              </div>
            </div>
          </div>

          {/* Popular Features Section - 作为首屏内容的一部分 */}
          <PopularFeatures />
          <TrustLogo />
        </div>
      </div>

      <ScrollAnimation
        direction="up"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <FeatureShowcase />
      </ScrollAnimation>

      <ScrollAnimation
        direction="left"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <MoreFeatures />
      </ScrollAnimation>

      <ScrollAnimation
        direction="right"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <WhyUs />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <UserCase />
      </ScrollAnimation>

      <ScrollAnimation
        direction="fade"
        delay={200}
        duration={1200}
        className="scroll-animation-container"
      >
        <ForEveryoneSection />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <TestimonialSection />
      </ScrollAnimation>

      <ScrollAnimation
        direction="left"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <FaqSectionPro />
      </ScrollAnimation>

      <ScrollAnimation
        direction="right"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <InterestRecommendationSection />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <CtaSection />
      </ScrollAnimation>

      <ScrollToTopButton />
    </div>
  )
}

export default Index
